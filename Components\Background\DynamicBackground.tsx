import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
// 🌅 CISCO: NETTOYAGE RADICAL - SEULS MODULES NÉCESSAIRES
import Diurn<PERSON><PERSON>ayer from './DiurnalLayer';
import ModeLeverSoleil from './ModeLeverSoleil';

// 🌅 CISCO: NETTOYAGE RADICAL - SEUL MODE AUTORISÉ
type BackgroundMode = 'leverSoleil';

interface DynamicBackgroundProps {
  skyMode?: string;
  children?: React.ReactNode;
}

const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ 
  skyMode = 'leverSoleil', 
  children 
}) => {
  // 🌅 CISCO: RÉFÉRENCES MINIMALES
  const landscapeRef = useRef<HTMLDivElement>(null);
  // zoomTimelineRef supprimé - animation zoom désactivée

  // 🌅 CISCO: FONCTION ZOOM/DÉZOOM PAYSAGE SUPPRIMÉE
  // Animation de zoom désactivée pour éliminer le "rectangle mobile suspect"

  // 🌅 CISCO: INITIALISATION MINIMALE
  useEffect(() => {
    console.log('🌅 CISCO: DynamicBackground CLEAN - Initialisation minimale');

    // 🔧 CISCO: DÉSACTIVATION animation zoom paysage (rectangle mobile suspect)
    // createLandscapeZoomAnimation(); // DÉSACTIVÉ pour éliminer mouvement non désiré

    // Forcer le paysage sombre au démarrage et FIXE
    if (landscapeRef.current) {
      gsap.set(landscapeRef.current, {
        filter: 'brightness(0.15)', // Très sombre au démarrage
        scale: 1.0, // 🔧 CISCO: Échelle fixe, pas de zoom
        transformOrigin: 'center center'
      });
      console.log('💡 Paysage initialisé SOMBRE et FIXE pour leverSoleil');
    }

    return () => {
      // Cleanup supprimé - plus d'animation zoom
    };
  }, []);

  return (
    <div className="fixed inset-0 w-full h-full overflow-hidden">
      {/* 🌅 CISCO: COUCHE NUAGES */}
      <DiurnalLayer skyMode={skyMode} />

      {/* 🌅 CISCO: MODULE LEVER DE SOLEIL COMPLET */}
      <ModeLeverSoleil
        isActive={skyMode === 'leverSoleil'}
        autoStart={false}
        intensity={0.0}
      />

      {/* 🌅 CISCO: PAYSAGE AVEC ZOOM/DÉZOOM */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: 'url(/Background.png)',
          backgroundPosition: 'center bottom -200px',
          backgroundSize: 'cover',
          zIndex: 10, // Au-dessus des autres couches
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />

      {/* 🌅 CISCO: CONTENU PRINCIPAL */}
      <div className="relative" style={{ zIndex: 15 }}>
        {children}
      </div>

      {/* 🌅 CISCO: STYLES GLOBAUX MINIMAUX */}
      <style dangerouslySetInnerHTML={{
        __html: `
          body, html {
            background: none !important;
            background-color: transparent !important;
            overflow-x: hidden !important;
            overflow-y: auto !important;
          }

          ::-webkit-scrollbar {
            width: 0px;
            background: transparent;
          }

          ::-webkit-scrollbar-track {
            background: transparent;
          }

          ::-webkit-scrollbar-thumb {
            background: transparent;
          }

          html {
            scrollbar-width: none;
          }

          body {
            -ms-overflow-style: none;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
