import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
// 🌅 CISCO: NETTOYAGE RADICAL - SEULS MODULES NÉCESSAIRES
import Diurnal<PERSON>ayer from './DiurnalLayer';
import ModeLeverSoleil from './ModeLeverSoleil';

// 🌅 CISCO: NETTOYAGE RADICAL - SEUL MODE AUTORISÉ
type BackgroundMode = 'leverSoleil';

interface DynamicBackgroundProps {
  skyMode?: string;
  children?: React.ReactNode;
}

const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ 
  skyMode = 'leverSoleil', 
  children 
}) => {
  // 🌅 CISCO: RÉFÉRENCES MINIMALES
  const landscapeRef = useRef<HTMLDivElement>(null);
  const zoomTimelineRef = useRef<gsap.core.Timeline | null>(null);

  // 🌅 CISCO: FONCTION ZOOM/DÉZOOM PAYSAGE (SAUVEGARDÉE)
  const createLandscapeZoomAnimation = () => {
    if (!landscapeRef.current) return;
    if (zoomTimelineRef.current) {
      zoomTimelineRef.current.kill();
    }
    zoomTimelineRef.current = gsap.timeline({ repeat: -1, yoyo: false, force3D: true });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 45, ease: "power2.inOut" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 5, ease: "none" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 35, ease: "power2.out" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 10, ease: "none" });
  };

  // 🌅 CISCO: INITIALISATION MINIMALE
  useEffect(() => {
    console.log('🌅 CISCO: DynamicBackground CLEAN - Initialisation minimale');
    
    // Démarrer l'animation zoom du paysage
    createLandscapeZoomAnimation();
    
    // Forcer le paysage sombre au démarrage
    if (landscapeRef.current) {
      gsap.set(landscapeRef.current, {
        filter: 'brightness(0.15)' // Très sombre au démarrage
      });
      console.log('💡 Paysage initialisé SOMBRE pour leverSoleil');
    }

    return () => {
      if (zoomTimelineRef.current) zoomTimelineRef.current.kill();
    };
  }, []);

  return (
    <div className="fixed inset-0 w-full h-full overflow-hidden">
      {/* 🌅 CISCO: COUCHE NUAGES */}
      <DiurnalLayer skyMode={skyMode} />

      {/* 🌅 CISCO: MODULE LEVER DE SOLEIL COMPLET */}
      <ModeLeverSoleil 
        isActive={skyMode === 'leverSoleil'} 
        autoStart={true}
        intensity={0.0}
      />

      {/* 🌅 CISCO: PAYSAGE AVEC ZOOM/DÉZOOM */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: 'url(/Background.png)',
          backgroundPosition: 'center bottom -200px',
          backgroundSize: 'cover',
          zIndex: 10, // Au-dessus des autres couches
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />

      {/* 🌅 CISCO: CONTENU PRINCIPAL */}
      <div className="relative" style={{ zIndex: 15 }}>
        {children}
      </div>

      {/* 🌅 CISCO: STYLES GLOBAUX MINIMAUX */}
      <style dangerouslySetInnerHTML={{
        __html: `
          body, html {
            background: none !important;
            background-color: transparent !important;
            overflow-x: hidden !important;
            overflow-y: auto !important;
          }

          ::-webkit-scrollbar {
            width: 0px;
            background: transparent;
          }

          ::-webkit-scrollbar-track {
            background: transparent;
          }

          ::-webkit-scrollbar-thumb {
            background: transparent;
          }

          html {
            scrollbar-width: none;
          }

          body {
            -ms-overflow-style: none;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
