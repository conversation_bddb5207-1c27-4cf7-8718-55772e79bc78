import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
// 🌟 CISCO: ÉTOILES SIMPLES À ZÉRO
import SimpleStars from './SimpleStars';

interface ModeLeverSoleilProps {
  isActive: boolean;
  intensity?: number; // 0-1, progression du lever de soleil
  autoStart?: boolean; // Démarrage automatique de la séquence
  onProgressionStart?: () => void; // Callback quand la progression démarre
  timerDuration?: number; // Durée du temporisateur en secondes (défaut: 60s)
  onTimerStart?: () => void; // Callback externe pour démarrer la progression
}

// 🎯 CISCO: Interface pour contrôle externe
export interface ModeLeverSoleilRef {
  startProgression: () => void;
  stopProgression: () => void;
  getCurrentPhase: () => string;
}

/**
 * 🌅 MODULE LEVER DE SOLEIL - TOUT EN UN
 *
 * CISCO INSTRUCTIONS: Ce module contient TOUT pour le lever du soleil
 * ✅ DÉMARRAGE EN MODE NUIT (tout sombre, étoiles visibles)
 * ✅ SONS NUIT → LEVER DE SOLEIL (temporisation)
 * ✅ Éclairage global spécifique au lever
 * ✅ Position du soleil qui se lève
 * ✅ Quelques étoiles qui disparaissent progressivement
 * ✅ Éclairage du paysage qui s'éclaircit
 * ✅ Dégradé de couleurs aube (monte du bas vers le haut)
 * ✅ TEMPORISATION AUTOMATIQUE progressive
 */
const ModeLeverSoleil: React.FC<ModeLeverSoleilProps> = ({
  isActive,
  intensity = 0.0, // 🔧 CISCO: Démarrage à 0 (nuit complète)
  autoStart = false, // 🔧 CISCO: Pas de démarrage automatique par défaut
  onProgressionStart,
  timerDuration = 60 // 🔧 CISCO: Durée par défaut 60 secondes
}) => {
  // 🎯 RÉFÉRENCES POUR LES ÉLÉMENTS
  const containerRef = useRef<HTMLDivElement>(null);
  const sunRef = useRef<HTMLDivElement>(null);
  const starsContainerRef = useRef<HTMLDivElement>(null);
  const globalLightRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 🌅 ÉTAT INTERNE POUR LA PROGRESSION AUTOMATIQUE
  const [currentIntensity, setCurrentIntensity] = useState(0.0); // Démarrage nuit complète
  const [isProgressing, setIsProgressing] = useState(false);
  const [currentPhase, setCurrentPhase] = useState<'nuit' | 'transition' | 'lever'>('nuit');

  // 🌅 PALETTES COULEURS NATURELLES - 4 PHASES DE 15 SECONDES (CISCO: recherche web)
  const COLOR_PHASES = {
    // 🌌 PHASE 1 (0-15s) : NUIT PROFONDE - Couleurs très sombres et apaisantes
    DEEP_NIGHT: {
      almostBlack: '#0B1426',    // Presque noir
      nightSky: '#041A40',       // Bleu nuit profond
      darkMidnight: '#001540',   // Bleu cétacé
      deepBlue: '#00224B'        // Bleu Oxford
    },

    // 🌃 PHASE 2 (15-30s) : FIN DE NUIT - Transition douce vers l'aube
    LATE_NIGHT: {
      midnightBlue: '#00316E',   // Bleu nuit profond
      darkBlue: '#00008B',       // Bleu foncé
      nightViolet: '#301934',    // Violet nocturne
      deepIndigo: '#1F214D'      // Indigo profond
    },

    // 🌅 PHASE 3 (30-45s) : AUBE - Premières lueurs douces et pastelles
    DAWN: {
      deepIndigo: '#1F214D',     // Indigo profond
      imperial: '#50366F',       // Violet impérial
      softRose: '#D8A7CA',       // Rose doux pastel
      warmPink: '#F4C2C2'       // Rose chaud pastel
    },

    // 🌄 PHASE 4 (45-60s) : LEVER DE SOLEIL - Couleurs chaudes et dorées
    SUNRISE: {
      softPink: '#F4C2C2',      // Rose doux
      peach: '#FFCBA4',         // Pêche pastel
      lightGold: '#FFE4B5',     // Or clair
      warmYellow: '#FFF8DC'     // Jaune chaud pastel
    }
  };

  // 🎵 GESTION AUDIO SELON CISCO INSTRUCTIONS
  const AUDIO_CONFIG = {
    night: '/sounds/nuit-profonde/night-atmosphere-with-crickets-374652.mp3',
    sunrise: '/sounds/lever-soleil/Lever_soleil-nature.mp3',
    transitionDelay: 30000, // 30 secondes avant transition audio
    fadeOutDuration: 5000,  // 5 secondes de fade out
    fadeInDuration: 3000    // 3 secondes de fade in
  };

  // 🌟 CISCO: ÉTOILES DÉPLACÉES DANS SimpleStars.tsx

  // 🎵 GESTION AUDIO PROGRESSIVE
  const playNightSound = () => {
    if (audioRef.current) {
      audioRef.current.pause();
    }

    audioRef.current = new Audio(AUDIO_CONFIG.night);
    audioRef.current.loop = true;
    audioRef.current.volume = 0.6;
    audioRef.current.play().catch(console.warn);
    console.log('🌙 Son de nuit démarré');
  };

  const transitionToSunriseSound = () => {
    if (!audioRef.current) return;

    // Fade out du son de nuit
    const fadeOutInterval = setInterval(() => {
      if (audioRef.current && audioRef.current.volume > 0.1) {
        audioRef.current.volume = Math.max(0, audioRef.current.volume - 0.1);
      } else {
        clearInterval(fadeOutInterval);
        if (audioRef.current) {
          audioRef.current.pause();
        }

        // Démarrer le son du lever de soleil
        audioRef.current = new Audio(AUDIO_CONFIG.sunrise);
        audioRef.current.loop = true;
        audioRef.current.volume = 0;
        audioRef.current.play().catch(console.warn);

        // Fade in du son du lever
        const fadeInInterval = setInterval(() => {
          if (audioRef.current && audioRef.current.volume < 0.5) {
            audioRef.current.volume = Math.min(0.5, audioRef.current.volume + 0.05);
          } else {
            clearInterval(fadeInInterval);
          }
        }, 100);

        console.log('🌅 Transition vers son du lever de soleil');
      }
    }, 100);
  };

  // 🌅 ANIMATION DU SOLEIL QUI SE LÈVE (CISCO: derrière le paysage)
  const animateSunrise = (progressIntensity: number) => {
    if (!sunRef.current) return;

    // CISCO: Position du soleil - commence SOUS l'horizon, monte progressivement
    const sunStartY = 150; // Bien sous l'horizon (150% du container)
    const sunEndY = 25;    // Position haute pour le lever (25% du container)
    const currentSunY = sunStartY - (progressIntensity * (sunStartY - sunEndY));

    // Taille et intensité du soleil selon progression
    const sunScale = 0.3 + (progressIntensity * 0.7); // 0.3 à 1.0 (plus de variation)
    const sunOpacity = progressIntensity < 0.1 ? 0 : Math.min((progressIntensity - 0.1) * 1.2, 1.0);

    // CISCO: Le soleil ne devient visible qu'au-dessus de l'horizon du paysage
    const isAboveHorizon = currentSunY < 100; // 100% = horizon du paysage

    gsap.to(sunRef.current, {
      y: `${currentSunY}%`,
      scale: sunScale,
      opacity: isAboveHorizon ? sunOpacity : 0,
      duration: 3.0, // Plus lent pour effet naturel
      ease: "power1.inOut"
    });
  };

  // ⭐ ANIMATION DES ÉTOILES QUI DISPARAISSENT (CISCO: progressif naturel)
  const animateStarsFading = (progressIntensity: number) => {
    if (!starsContainerRef.current) return;

    // CISCO: Les étoiles disparaissent progressivement - plus naturel
    let starsOpacity;
    if (progressIntensity < 0.2) {
      starsOpacity = 1.0; // Pleine visibilité au début
    } else if (progressIntensity < 0.6) {
      starsOpacity = 1.0 - ((progressIntensity - 0.2) * 1.5); // Disparition progressive
    } else {
      starsOpacity = 0; // Complètement invisibles
    }

    gsap.to(starsContainerRef.current, {
      opacity: Math.max(starsOpacity, 0),
      duration: 4.0, // Plus lent pour effet naturel
      ease: "power2.out"
    });
  };

  // 💡 ÉCLAIRAGE GLOBAL PROGRESSIF (CISCO: synchronisé avec le soleil)
  const animateGlobalLighting = (progressIntensity: number) => {
    if (!globalLightRef.current) return;

    // CISCO: L'éclairage global suit la position du soleil
    let lightIntensity;
    if (progressIntensity < 0.1) {
      lightIntensity = 0; // Nuit complète
    } else if (progressIntensity < 0.5) {
      lightIntensity = (progressIntensity - 0.1) * 0.5; // Montée progressive
    } else {
      lightIntensity = 0.2 + (progressIntensity - 0.5) * 0.6; // Intensification
    }

    gsap.to(globalLightRef.current, {
      opacity: lightIntensity,
      duration: 3.0,
      ease: "power1.inOut"
    });
  };

  // 🏔️ ÉCLAIRAGE DU PAYSAGE (CISCO: s'éclaircit progressivement)
  const animateLandscapeLighting = (progressIntensity: number) => {
    if (!landscapeRef.current) return;

    // CISCO: Le paysage s'éclaircit avec le lever du soleil
    let brightness;
    if (progressIntensity < 0.1) {
      brightness = 0.15; // Très sombre au début (nuit)
    } else if (progressIntensity < 0.7) {
      brightness = 0.15 + (progressIntensity - 0.1) * 1.0; // Éclaircissement progressif
    } else {
      brightness = 0.75 + (progressIntensity - 0.7) * 0.5; // Pleine lumière
    }

    gsap.to(landscapeRef.current, {
      filter: `brightness(${brightness})`,
      duration: 3.5,
      ease: "power1.inOut"
    });
  };

  // 🎨 DÉGRADÉ DYNAMIQUE 4 PHASES - 60 SECONDES (CISCO: système temporisé)
  const createSunriseGradient = (progressIntensity: number) => {
    if (!containerRef.current) return;

    // 🔧 CISCO: SYSTÈME 4 PHASES DE 15 SECONDES CHACUNE
    let gradient;

    if (progressIntensity < 0.25) {
      // 🌌 PHASE 1 (0-15s) : NUIT PROFONDE - Tout sombre et apaisant
      gradient = `linear-gradient(to top,
        ${COLOR_PHASES.DEEP_NIGHT.almostBlack} 0%,
        ${COLOR_PHASES.DEEP_NIGHT.nightSky} 40%,
        ${COLOR_PHASES.DEEP_NIGHT.darkMidnight} 70%,
        ${COLOR_PHASES.DEEP_NIGHT.almostBlack} 100%)`;
    } else if (progressIntensity < 0.5) {
      // 🌃 PHASE 2 (15-30s) : FIN DE NUIT - Transition douce
      gradient = `linear-gradient(to top,
        ${COLOR_PHASES.LATE_NIGHT.midnightBlue} 0%,
        ${COLOR_PHASES.LATE_NIGHT.darkBlue} 30%,
        ${COLOR_PHASES.LATE_NIGHT.nightViolet} 60%,
        ${COLOR_PHASES.LATE_NIGHT.deepIndigo} 100%)`;
    } else if (progressIntensity < 0.75) {
      // 🌅 PHASE 3 (30-45s) : AUBE - Premières lueurs pastelles
      gradient = `linear-gradient(to top,
        ${COLOR_PHASES.DAWN.deepIndigo} 0%,
        ${COLOR_PHASES.DAWN.imperial} 25%,
        ${COLOR_PHASES.DAWN.softRose} 60%,
        ${COLOR_PHASES.DAWN.warmPink} 100%)`;
    } else {
      // 🌄 PHASE 4 (45-60s) : LEVER DE SOLEIL - Couleurs chaudes et dorées
      gradient = `linear-gradient(to top,
        ${COLOR_PHASES.SUNRISE.softPink} 0%,
        ${COLOR_PHASES.SUNRISE.peach} 25%,
        ${COLOR_PHASES.SUNRISE.lightGold} 60%,
        ${COLOR_PHASES.SUNRISE.warmYellow} 100%)`;
    }

    gsap.to(containerRef.current, {
      backgroundImage: gradient,
      duration: 3.75, // 15 secondes / 4 = 3.75s par transition
      ease: "power1.inOut"
    });
  };

  // 🎬 ORCHESTRATION COMPLÈTE DU LEVER DE SOLEIL (CISCO: tout synchronisé)
  const orchestrateSunrise = (progressIntensity: number) => {
    console.log(`🌅 CISCO Orchestration lever de soleil - Intensité: ${progressIntensity.toFixed(2)} - Phase: ${currentPhase}`);

    // Tuer l'animation précédente si elle existe
    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    // Créer une nouvelle timeline synchronisée
    timelineRef.current = gsap.timeline();

    // CISCO: Lancer toutes les animations en parallèle - TOUT synchronisé
    createSunriseGradient(progressIntensity);
    animateSunrise(progressIntensity);
    animateStarsFading(progressIntensity);
    animateGlobalLighting(progressIntensity);
    animateLandscapeLighting(progressIntensity);

    // CISCO: Gestion des phases et transitions audio
    if (progressIntensity < 0.1 && currentPhase !== 'nuit') {
      setCurrentPhase('nuit');
    } else if (progressIntensity >= 0.1 && progressIntensity < 0.6 && currentPhase !== 'transition') {
      const newPhase = 'transition';
      setCurrentPhase(newPhase);
      // Transition audio après 30 secondes
      setTimeout(() => {
        // Vérifier que nous sommes toujours en phase de transition
        setCurrentPhase(prevPhase => {
          if (prevPhase === 'transition') {
            transitionToSunriseSound();
          }
          return prevPhase;
        });
      }, AUDIO_CONFIG.transitionDelay);
    } else if (progressIntensity >= 0.6 && currentPhase !== 'lever') {
      setCurrentPhase('lever');
    }
  };

  // 🕐 TEMPORISATION AUTOMATIQUE (CISCO: progression basée sur durée temporisateur)
  const startAutomaticProgression = () => {
    if (isProgressing) return;

    console.log(`🌅 CISCO: Démarrage progression automatique du lever de soleil (${timerDuration}s)`);
    setIsProgressing(true);
    setCurrentIntensity(0.0); // Démarrage nuit complète

    // Démarrer le son de nuit
    playNightSound();

    // Callback pour notifier le démarrage
    if (onProgressionStart) {
      onProgressionStart();
    }

    // 🔧 CISCO: Progression automatique EXACTEMENT 60 secondes - 4 phases de 15s
    const totalDuration = 60 * 1000; // 60 secondes en millisecondes
    const updateInterval = 250; // Mise à jour toutes les 250ms pour fluidité
    const incrementPerUpdate = 1.0 / (totalDuration / updateInterval); // 0.004167 par update

    const progressionInterval = setInterval(() => {
      setCurrentIntensity(prev => {
        const newIntensity = prev + incrementPerUpdate;

        if (newIntensity >= 1.0) {
          clearInterval(progressionInterval);
          setIsProgressing(false);
          console.log('🌅 CISCO: Lever de soleil terminé');
          return 1.0;
        }

        return newIntensity;
      });
    }, updateInterval); // 250ms pour fluidité

    // Nettoyer l'intervalle si le composant se démonte
    return () => clearInterval(progressionInterval);
  };

  // 🎯 CISCO: Méthodes publiques pour contrôle externe
  const startProgression = () => {
    if (!isProgressing) {
      console.log('🌅 CISCO: Démarrage progression externe (temporisateur)');
      startAutomaticProgression();
    }
  };

  const stopProgression = () => {
    console.log('🌅 CISCO: Arrêt de la progression du lever de soleil');
    setIsProgressing(false);

    // Arrêter l'audio
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }

    // Remettre en mode nuit
    setCurrentIntensity(0.0);
    setCurrentPhase('nuit');
    orchestrateSunrise(0.0);
  };

  const getCurrentPhase = () => {
    if (currentIntensity < 0.25) return 'Nuit profonde';
    if (currentIntensity < 0.5) return 'Fin de nuit';
    if (currentIntensity < 0.75) return 'Aube';
    return 'Lever de soleil';
  };

  // 🔧 CISCO: Exposer les méthodes pour contrôle externe via window
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).sunriseControls = {
        start: startProgression,
        stop: stopProgression,
        getPhase: getCurrentPhase,
        getIntensity: () => currentIntensity
      };
    }
  }, [currentIntensity, isProgressing]);

  // 🔄 EFFET PRINCIPAL - RÉACTION AUX CHANGEMENTS (CISCO: démarrage en mode nuit)
  useEffect(() => {
    if (isActive) {
      console.log('🌅 CISCO: ACTIVATION MODULE LEVER DE SOLEIL - Démarrage nuit complète');

      // 🔧 CISCO: FORCER IMMÉDIATEMENT LE MODE NUIT - Éviter le flash
      if (containerRef.current) {
        // Appliquer immédiatement le dégradé nuit sans animation
        containerRef.current.style.backgroundImage = `linear-gradient(to top,
          ${COLOR_PHASES.DEEP_NIGHT.almostBlack} 0%,
          ${COLOR_PHASES.DEEP_NIGHT.nightSky} 50%,
          ${COLOR_PHASES.DEEP_NIGHT.almostBlack} 100%)`;
      }

      // CISCO: TOUJOURS démarrer en mode nuit (intensité 0) - PAS de progression automatique
      // La progression ne se lance que quand le temporisateur démarre
      orchestrateSunrise(0.0); // Force le mode nuit au démarrage

      // CISCO: Démarrage automatique SEULEMENT si explicitement demandé
      if (autoStart && !isProgressing) {
        console.log('🌅 CISCO: AutoStart activé - Lancement progression automatique');
        startAutomaticProgression();
      }
    } else {
      console.log('🌅 CISCO: DÉSACTIVATION MODULE LEVER DE SOLEIL');

      // Arrêter la progression automatique
      setIsProgressing(false);

      // Arrêter l'audio
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // Fade out en douceur
      if (containerRef.current) {
        gsap.to(containerRef.current, {
          opacity: 0,
          duration: 2.0,
          ease: "power2.out"
        });
      }
    }

    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, [isActive, autoStart]); // Suppression de 'intensity' pour éviter les re-renders

  // 🔄 EFFET POUR LA PROGRESSION AUTOMATIQUE
  useEffect(() => {
    if (isProgressing && isActive) {
      orchestrateSunrise(currentIntensity);
    }
  }, [currentIntensity, isProgressing, isActive]);

  // 🌟 CISCO: ÉTOILES GÉRÉES PAR SimpleStars.tsx

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 pointer-events-none"
      style={{
        zIndex: 5, // CISCO: Au-dessus du background de base, sous le paysage
        opacity: isActive ? 1 : 0,
        transition: 'opacity 2s ease-in-out',
        // 🔧 CISCO: DÉMARRAGE NUIT NOIRE FORCÉ - Éviter le flash
        backgroundImage: `linear-gradient(to top,
          ${COLOR_PHASES.DEEP_NIGHT.almostBlack} 0%,
          ${COLOR_PHASES.DEEP_NIGHT.nightSky} 50%,
          ${COLOR_PHASES.DEEP_NIGHT.almostBlack} 100%)`
      }}
    >
      {/* 🌅 SOLEIL LEVANT (CISCO: derrière le paysage) */}
      <div
        ref={sunRef}
        className="absolute"
        style={{
          left: '75%', // Position à l'est pour le lever
          top: '50%',
          transform: 'translate(-50%, -50%)',
          width: '140px', // Légèrement plus grand
          height: '140px',
          borderRadius: '50%',
          background: `radial-gradient(circle,
            ${COLOR_PHASES.SUNRISE.warmYellow} 0%,
            ${COLOR_PHASES.SUNRISE.lightGold} 25%,
            ${COLOR_PHASES.SUNRISE.peach} 60%,
            rgba(255, 203, 164, 0.3) 85%,
            transparent 100%)`,
          boxShadow: `
            0 0 80px ${COLOR_PHASES.SUNRISE.lightGold}40,
            0 0 120px ${COLOR_PHASES.SUNRISE.peach}20,
            inset 0 0 40px ${COLOR_PHASES.SUNRISE.warmYellow}60
          `,
          opacity: 0,
          zIndex: 8 // CISCO: Derrière le paysage (z-index 10)
        }}
      />

      {/* ⭐ ÉTOILES SIMPLES À ZÉRO (CISCO: composant dédié) */}
      <SimpleStars
        isVisible={isActive}
        opacity={currentIntensity < 0.6 ? (1 - currentIntensity * 1.5) : 0}
      />

      {/* 💡 ÉCLAIRAGE GLOBAL PROGRESSIF (CISCO: synchronisé avec le soleil) */}
      <div
        ref={globalLightRef}
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at 75% 60%,
            ${COLOR_PHASES.SUNRISE.warmYellow}15 0%,
            ${COLOR_PHASES.SUNRISE.peach}08 30%,
            ${COLOR_PHASES.SUNRISE.softPink}05 50%,
            transparent 75%)`,
          opacity: 0,
          zIndex: 6
        }}
      />

      {/* 🏔️ RÉFÉRENCE PAYSAGE POUR ÉCLAIRAGE (CISCO: s'éclaircit progressivement) */}
      <div
        ref={landscapeRef}
        className="absolute inset-0"
        style={{
          background: 'transparent',
          filter: 'brightness(0.15)', // Démarrage très sombre
          zIndex: 7
        }}
      />

      {/* 🎨 STYLES POUR ANIMATIONS (CISCO: scintillement naturel) */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* CISCO: Animation étoiles déplacée dans SimpleStars.tsx */

          /* CISCO: Styles pour debug et info */
          .sunrise-debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
          }
        `
      }} />

      {/* 🔧 CISCO: DEBUG INFO (optionnel) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="sunrise-debug">
          <div>🌅 ModeLeverSoleil.tsx</div>
          <div>Intensité: {(isProgressing ? currentIntensity : intensity).toFixed(3)}</div>
          <div>Phase: {currentPhase}</div>
          <div>Progression: {isProgressing ? 'OUI' : 'NON'}</div>
          <div>Étoiles: SimpleStars.tsx</div>
        </div>
      )}
    </div>
  );
};

export default ModeLeverSoleil;
