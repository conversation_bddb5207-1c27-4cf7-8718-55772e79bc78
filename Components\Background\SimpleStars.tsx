import React, { useEffect, useRef } from 'react';

interface SimpleStarsProps {
  isVisible: boolean;
  opacity: number; // 0-1 pour la disparition progressive
}

/**
 * 🌟 ÉTOILES SIMPLES À ZÉRO (CISCO: scintillement FIXE, pas de mouvement)
 * 
 * Composant dédié uniquement aux étoiles pour éviter les conflits
 * - Positions FIXES (pas de mouvement)
 * - Scintillement simple (opacity seulement)
 * - D<PERSON>lais très espacés pour éviter synchronisation
 */
const SimpleStars: React.FC<SimpleStarsProps> = ({ isVisible, opacity }) => {
  const starsContainerRef = useRef<HTMLDivElement>(null);

  // 🌟 GÉNÉRATION ÉTOILES SIMPLES
  const generateStars = () => {
    const stars = [];
    const starCount = 15; // Nombre réduit pour éviter les problèmes
    
    for (let i = 0; i < starCount; i++) {
      const star = {
        id: i,
        x: Math.random() * 100, // Position FIXE en %
        y: Math.random() * 60,  // Position FIXE en %
        size: 1 + Math.random() * 2, // 1-3px
        baseOpacity: 0.5 + Math.random() * 0.5, // 0.5-1.0
        // Délais TRÈS espacés pour éviter synchronisation
        animationDelay: Math.random() * 20, // 0-20 secondes
        animationDuration: 2 + Math.random() * 4 // 2-6 secondes
      };
      stars.push(star);
    }
    return stars;
  };

  const stars = generateStars();

  return (
    <div
      ref={starsContainerRef}
      className="fixed inset-0 pointer-events-none"
      style={{ 
        zIndex: 9999, // Au-dessus de tout
        opacity: isVisible ? opacity : 0,
        transition: 'opacity 3s ease-out'
      }}
    >
      {stars.map((star) => (
        <div
          key={star.id}
          className="absolute rounded-full bg-white"
          style={{
            left: `${star.x}%`,
            top: `${star.y}%`,
            width: `${star.size}px`,
            height: `${star.size}px`,
            opacity: star.baseOpacity,
            // Animation CSS simple sans mouvement
            animation: `simpleTwinkle ${star.animationDuration}s ease-in-out infinite ${star.animationDelay}s`,
            boxShadow: `0 0 ${star.size * 2}px rgba(255, 255, 255, 0.3)`
          }}
        />
      ))}

      {/* 🌟 ANIMATION CSS SIMPLE */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes simpleTwinkle {
            0% { 
              opacity: 0.2;
            }
            50% { 
              opacity: 1;
            }
            100% { 
              opacity: 0.2;
            }
          }
        `
      }} />
    </div>
  );
};

export default SimpleStars;
